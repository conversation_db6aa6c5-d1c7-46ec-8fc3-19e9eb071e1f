use std::path::Path;

pub fn format_file_size(size: u64) -> String {
    const UNITS: &[&str] = &["B", "KB", "MB", "GB", "TB"];
    let mut size = size as f64;
    let mut unit_index = 0;
    
    while size >= 1024.0 && unit_index < UNITS.len() - 1 {
        size /= 1024.0;
        unit_index += 1;
    }
    
    if unit_index == 0 {
        format!("{} {}", size as u64, UNITS[unit_index])
    } else {
        format!("{:.1} {}", size, UNITS[unit_index])
    }
}

pub fn get_file_icon(path: &Path) -> &'static str {
    if let Some(extension) = path.extension() {
        match extension.to_string_lossy().to_lowercase().as_str() {
            "jpg" | "jpeg" | "png" | "gif" | "bmp" | "webp" => "🖼️",
            "mp4" | "avi" | "mkv" | "mov" | "wmv" | "flv" => "🎬",
            "mp3" | "wav" | "flac" | "aac" | "ogg" => "🎵",
            "pdf" => "📄",
            "doc" | "docx" => "📝",
            "xls" | "xlsx" => "📊",
            "ppt" | "pptx" => "📈",
            "zip" | "rar" | "7z" | "tar" | "gz" => "📦",
            "exe" | "msi" => "⚙️",
            "txt" | "md" | "log" => "📃",
            _ => "📄",
        }
    } else {
        "📄"
    }
}

pub fn truncate_path(path: &Path, max_length: usize) -> String {
    let path_str = path.to_string_lossy();
    if path_str.len() <= max_length {
        path_str.to_string()
    } else {
        let start = &path_str[..max_length / 2 - 2];
        let end = &path_str[path_str.len() - max_length / 2 + 2..];
        format!("{}...{}", start, end)
    }
}
