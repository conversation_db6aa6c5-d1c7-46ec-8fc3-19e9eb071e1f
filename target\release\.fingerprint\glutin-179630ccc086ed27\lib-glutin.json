{"rustc": 16591470773350601817, "features": "[\"default\", \"egl\", \"glutin_egl_sys\", \"glutin_glx_sys\", \"glutin_wgl_sys\", \"glx\", \"libloading\", \"wayland\", \"wayland-sys\", \"wgl\", \"windows-sys\", \"x11\", \"x11-dl\"]", "declared_features": "[\"default\", \"egl\", \"glutin_egl_sys\", \"glutin_glx_sys\", \"glutin_wgl_sys\", \"glx\", \"libloading\", \"wayland\", \"wayland-sys\", \"wgl\", \"windows-sys\", \"x11\", \"x11-dl\"]", "target": 1390373939866593923, "profile": 16503403049695105087, "path": 241404933892219847, "deps": [[3605981442814366586, "glutin_wgl_sys", false, 2765703120491257771], [3722963349756955755, "once_cell", false, 8276013307107028124], [8325503527859029417, "build_script_build", false, 14143779504703536346], [10435729446543529114, "bitflags", false, 10064231437202875826], [11669989806873621205, "libloading", false, 15907265534895879640], [11693073011723388840, "raw_window_handle", false, 7747968477157517573], [16021738386515941833, "glutin_egl_sys", false, 3508753823605344825], [17191429306861015010, "windows_sys", false, 1029315327592111148]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\glutin-179630ccc086ed27\\dep-lib-glutin", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}