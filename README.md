# DupKiller - Duplicate File Finder

A fast, efficient duplicate file finder with a modern GUI built in Rust using egui.

## Features

- **Fast Scanning**: Multi-threaded file scanning with progress tracking
- **Smart Detection**: Uses file size, partial hash, and full hash comparison for accurate duplicate detection
- **Flexible Filtering**: Filter by file extensions, MIME types, size range, and age
- **Modern GUI**: Clean, responsive interface with file preview
- **Batch Operations**: Delete duplicates by age (newest/oldest) or by directory preference
- **Cross-Platform**: Works on Windows, macOS, and Linux
- **Safe Operations**: Option to move files to recycle bin instead of permanent deletion

## Installation

### Prerequisites

- Rust 1.70 or later
- Cargo (comes with Rust)

### Building from Source

```bash
git clone <repository-url>
cd dupkiller

# Check syntax and run tests
cargo check
cargo test

# Build release version
cargo build --release
```

The executable will be available at `target/release/dupkiller` (or `dupkiller.exe` on Windows).

### Quick Start Scripts

- `check_syntax.bat` - Verify Rust installation and check code syntax
- `build.bat` - Build the release version
- `run.bat` - Run the application directly

## Usage

1. **Launch the application**:
   ```bash
   cargo run --release
   ```

2. **Add folders to scan**:
   - Click "Add Folder" to select directories
   - You can add multiple folders for scanning

3. **Configure filters** (optional):
   - Expand the "Filters" section
   - Set file extension filters
   - Configure size and age ranges
   - Set MIME type filters

4. **Start scanning**:
   - Click "Scan" to begin the duplicate detection process
   - Progress will be shown in the top panel

5. **Review results**:
   - Duplicate sets will be displayed in collapsible groups
   - Each group shows the number of files and total/wasted space
   - Click on files to preview them in the side panel

6. **Take action**:
   - Use bulk actions: "Delete All But Newest" or "Delete All But Oldest"
   - Or delete individual files using the delete button
   - Use the recycle bin option for safer deletion

## Architecture

The application is structured into several modules:

- **Core**: File scanning, duplicate detection, and file operations
  - `FileScanner`: Walks directories and applies filters
  - `DuplicateFinder`: Implements the duplicate detection algorithm
  - `FileOperations`: Handles file deletion and recycling

- **GUI**: User interface components
  - `DuplicateFinderApp`: Main application window
  - `DuplicateView`: Displays duplicate file sets
  - `FilePreview`: Shows file details and preview

- **Utils**: Utility functions for formatting and file handling

## Algorithm

The duplicate detection uses a three-stage approach for efficiency:

1. **Size Grouping**: Group files by size (files with different sizes cannot be duplicates)
2. **Partial Hash**: Compute hash of first 4KB for files in each size group
3. **Full Hash**: Compute full file hash only for files with matching partial hashes

This approach minimizes I/O operations while maintaining accuracy.

## Dependencies

- `eframe/egui`: Modern immediate mode GUI framework
- `walkdir`: Recursive directory traversal
- `sha2`: SHA-256 hashing for file comparison
- `rayon`: Data parallelism for performance
- `rfd`: Native file dialogs
- `mime_guess`: MIME type detection
- `chrono`: Date/time handling
- `trash`: Cross-platform recycle bin/trash functionality

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.
