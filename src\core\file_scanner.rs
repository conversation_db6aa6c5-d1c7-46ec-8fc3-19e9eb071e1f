use std::path::{Path, PathBuf};
use std::time::SystemTime;
use walkdir::WalkDir;
use std::fs;
use mime_guess::MimeGuess;

#[derive(Clone)]
pub struct FileFilter {
    pub extensions: Option<Vec<String>>,
    pub mime_types: Option<Vec<String>>,
    pub min_size: Option<u64>,
    pub max_size: Option<u64>,
    pub min_age: Option<SystemTime>,
    pub max_age: Option<SystemTime>,
}

impl Default for FileFilter {
    fn default() -> Self {
        Self {
            extensions: None,
            mime_types: None,
            min_size: None,
            max_size: None,
            min_age: None,
            max_age: None,
        }
    }
}

#[derive(Clone)]
pub struct FileInfo {
    pub path: PathBuf,
    pub size: u64,
    pub modified: SystemTime,
    pub hash: Option<String>,
}

pub struct FileScanner {
    filters: FileFilter,
}

impl FileScanner {
    pub fn new(filters: FileFilter) -> Self {
        Self { filters }
    }

    pub fn scan_directory(&self, dir_path: &Path) -> Vec<FileInfo> {
        let mut files = Vec::new();

        for entry in WalkDir::new(dir_path).into_iter().filter_map(|e| e.ok()) {
            if !entry.file_type().is_file() {
                continue;
            }

            let path = entry.path().to_path_buf();
            
            // Apply extension filter
            if let Some(ref extensions) = self.filters.extensions {
                if let Some(ext) = path.extension() {
                    if !extensions.iter().any(|e| e.eq_ignore_ascii_case(ext.to_string_lossy().as_ref())) {
                        continue;
                    }
                } else {
                    continue;
                }
            }

            // Get file metadata
            let metadata = match fs::metadata(&path) {
                Ok(meta) => meta,
                Err(_) => continue,
            };

            let size = metadata.len();
            let modified = metadata.modified().unwrap_or(SystemTime::UNIX_EPOCH);

            // Apply size filter
            if let Some(min_size) = self.filters.min_size {
                if size < min_size {
                    continue;
                }
            }
            if let Some(max_size) = self.filters.max_size {
                if size > max_size {
                    continue;
                }
            }

            // Apply age filter
            if let Some(min_age) = self.filters.min_age {
                if modified > min_age {
                    continue;
                }
            }
            if let Some(max_age) = self.filters.max_age {
                if modified < max_age {
                    continue;
                }
            }

            // Apply mime type filter
            if let Some(ref mime_types) = self.filters.mime_types {
                let guess = MimeGuess::from_path(&path);
                let mime = guess.first_or_octet_stream().to_string();
                if !mime_types.iter().any(|m| mime.starts_with(m)) {
                    continue;
                }
            }

            files.push(FileInfo {
                path,
                size,
                modified,
                hash: None,
            });
        }

        files
    }
}