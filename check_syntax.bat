@echo off
echo Checking if Rust is installed...
cargo --version >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo Rust/Cargo is not installed or not in PATH.
    echo Please install Rust from https://rustup.rs/
    echo Then restart your terminal and try again.
    pause
    exit /b 1
)

echo Rust is installed. Checking syntax...
echo.
echo Downloading dependencies and checking syntax...
cargo check
if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✅ Syntax check passed!
    echo.
    echo Running tests...
    cargo test
    if %ERRORLEVEL% EQU 0 (
        echo.
        echo ✅ All tests passed!
        echo ✅ Ready to build and run!
        echo.
        echo To build: cargo build --release
        echo To run:   cargo run --release
    ) else (
        echo.
        echo ⚠️  Some tests failed, but syntax is OK.
        echo You can still build and run the application.
    )
) else (
    echo.
    echo ❌ Syntax errors found. Please fix them before building.
    echo Check the error messages above for details.
    exit /b 1
)
pause
