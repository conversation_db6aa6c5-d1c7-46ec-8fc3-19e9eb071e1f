@echo off
echo Checking if Rust is installed...
cargo --version >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo Rust/Cargo is not installed or not in PATH.
    echo Please install Rust from https://rustup.rs/
    echo Then restart your terminal and try again.
    pause
    exit /b 1
)

echo Rust is installed. Checking syntax...
cargo check
if %ERRORLEVEL% EQU 0 (
    echo ✅ Syntax check passed!
    echo Running tests...
    cargo test
    if %ERRORLEVEL% EQU 0 (
        echo ✅ All tests passed!
        echo Ready to build and run!
    ) else (
        echo ❌ Some tests failed, but syntax is OK.
    )
) else (
    echo ❌ Syntax errors found. Please fix them before building.
    exit /b 1
)
pause
