use std::fs;
use std::path::{Path, PathBuf};
use std::io;

pub struct FileOperations;

impl FileOperations {
    pub fn delete_file(path: &Path) -> io::Result<()> {
        fs::remove_file(path)
    }
    
    #[cfg(target_os = "windows")]
    pub fn recycle_file(path: &Path) -> io::Result<()> {
        use std::os::windows::fs::MetadataExt;
        
        // On Windows, we can use the shell32 API to send to recycle bin
        let path_wide: Vec<u16> = path.as_os_str().encode_wide().chain(Some(0)).collect();
        
        let mut file_op = unsafe { std::mem::zeroed::<winapi::um::shellapi::SHFILEOPSTRUCTW>() };
        file_op.wFunc = winapi::um::shellapi::FO_DELETE;
        file_op.pFrom = path_wide.as_ptr();
        file_op.fFlags = winapi::um::shellapi::FOF_ALLOWUNDO | winapi::um::shellapi::FOF_NOCONFIRMATION | winapi::um::shellapi::FOF_SILENT;
        
        let result = unsafe { winapi::um::shellapi::SHFileOperationW(&mut file_op) };
        
        if result == 0 {
            Ok(())
        } else {
            Err(io::Error::new(io::ErrorKind::Other, "Failed to recycle file"))
        }
    }
    
    #[cfg(not(target_os = "windows"))]
    pub fn recycle_file(path: &Path) -> io::Result<()> {
        // For non-Windows platforms, we'll use trash-cli if available
        // This is a simplified implementation
        use std::process::Command;
        
        let output = Command::new("trash")
            .arg(path.as_os_str())
            .output()?;
            
        if output.status.success() {
            Ok(())
        } else {
            Err(io::Error::new(io::ErrorKind::Other, "Failed to move file to trash"))
        }
    }
    
    pub fn preserve_newest(paths: &[PathBuf]) -> io::Result<()> {
        if paths.len() <= 1 {
            return Ok(());
        }
        
        // Find the newest file
        let mut newest_path = &paths[0];
        let mut newest_time = fs::metadata(newest_path)?.modified()?;
        
        for path in &paths[1..] {
            let modified = fs::metadata(path)?.modified()?;
            if modified > newest_time {
                newest_time = modified;
                newest_path = path;
            }
        }
        
        // Delete all except the newest
        for path in paths {
            if path != newest_path {
                Self::delete_file(path)?;
            }
        }
        
        Ok(())
    }
    
    pub fn preserve_oldest(paths: &[PathBuf]) -> io::Result<()> {
        if paths.len() <= 1 {
            return Ok(());
        }
        
        // Find the oldest file
        let mut oldest_path = &paths[0];
        let mut oldest_time = fs::metadata(oldest_path)?.modified()?;
        
        for path in &paths[1..] {
            let modified = fs::metadata(path)?.modified()?;
            if modified < oldest_time {
                oldest_time = modified;
                oldest_path = path;
            }
        }
        
        // Delete all except the oldest
        for path in paths {
            if path != oldest_path {
                Self::delete_file(path)?;
            }
        }
        
        Ok(())
    }
    
    pub fn preserve_from_directory(paths: &[PathBuf], preserve_dir: &Path) -> io::Result<()> {
        for path in paths {
            if !path.starts_with(preserve_dir) {
                Self::delete_file(path)?;
            }
        }
        
        Ok(())
    }
}