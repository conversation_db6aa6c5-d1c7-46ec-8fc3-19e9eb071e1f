{"rustc": 16591470773350601817, "features": "[\"bytemuck\", \"default_fonts\", \"log\"]", "declared_features": "[\"bytemuck\", \"cint\", \"color-hex\", \"deadlock_detection\", \"default\", \"default_fonts\", \"document-features\", \"extra_asserts\", \"extra_debug_asserts\", \"log\", \"mint\", \"serde\", \"unity\"]", "target": 10495837225410426609, "profile": 16503403049695105087, "path": 1052012967234777799, "deps": [[966925859616469517, "ahash", false, 13804653977845464945], [2062481783838671931, "parking_lot", false, 2109236853437183333], [3955665883727284124, "ecolor", false, 15158855259349432163], [5931649091606299019, "nohash_hasher", false, 9612945154726014965], [5986029879202738730, "log", false, 14602815359518871829], [9447148682944742144, "ab_glyph", false, 69224922792755738], [10104497354789709585, "emath", false, 1234856782163839015], [14074610438553418890, "bytemuck", false, 7257443027616428165]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\epaint-1c3174835ab6c012\\dep-lib-epaint", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}