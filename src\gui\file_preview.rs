use eframe::egui;
use std::path::Path;
use crate::core::FileInfo;
use crate::utils::{format_file_size, get_file_icon};
use chrono::{DateTime, Utc};

pub struct FilePreview;

impl FilePreview {
    pub fn show(ui: &mut egui::Ui, file: &FileInfo) {
        ui.vertical(|ui| {
            // File icon and name
            ui.horizontal(|ui| {
                ui.label(get_file_icon(&file.path));
                ui.label(file.path.file_name()
                    .unwrap_or_default()
                    .to_string_lossy()
                    .to_string());
            });
            
            ui.separator();
            
            // File details
            ui.label(format!("Size: {}", format_file_size(file.size)));
            
            if let Ok(modified) = file.modified.duration_since(std::time::UNIX_EPOCH) {
                let datetime = chrono::DateTime::from_timestamp(modified.as_secs() as i64, 0)
                    .unwrap_or_default();
                ui.label(format!("Modified: {}", datetime.format("%Y-%m-%d %H:%M:%S")));
            }
            
            ui.label(format!("Path: {}", file.path.to_string_lossy()));
            
            if let Some(ref hash) = file.hash {
                ui.label(format!("Hash: {}...", &hash[..16]));
            }
            
            ui.separator();
            
            // Action buttons
            ui.horizontal(|ui| {
                if ui.button("Open").clicked() {
                    Self::open_file(&file.path);
                }
                
                if ui.button("Show in Explorer").clicked() {
                    Self::show_in_explorer(&file.path);
                }
                
                if ui.button("Copy Path").clicked() {
                    ui.output_mut(|o| o.copied_text = file.path.to_string_lossy().to_string());
                }
            });
            
            // Preview content for certain file types
            if Self::can_preview(&file.path) {
                ui.separator();
                ui.label("Preview:");
                Self::show_preview_content(ui, &file.path);
            }
        });
    }
    
    fn can_preview(path: &Path) -> bool {
        if let Some(extension) = path.extension() {
            matches!(extension.to_string_lossy().to_lowercase().as_str(),
                "txt" | "md" | "log" | "json" | "xml" | "csv" | "ini" | "cfg" | "conf")
        } else {
            false
        }
    }
    
    fn show_preview_content(ui: &mut egui::Ui, path: &Path) {
        match std::fs::read_to_string(path) {
            Ok(content) => {
                let preview = if content.len() > 1000 {
                    format!("{}...\n\n[File truncated - {} total characters]", 
                           &content[..1000], content.len())
                } else {
                    content
                };
                
                egui::ScrollArea::vertical()
                    .max_height(200.0)
                    .show(ui, |ui| {
                        ui.add(egui::TextEdit::multiline(&mut preview.as_str())
                            .desired_width(f32::INFINITY)
                            .font(egui::TextStyle::Monospace));
                    });
            }
            Err(e) => {
                ui.label(format!("Cannot preview: {}", e));
            }
        }
    }
    
    fn open_file(path: &Path) {
        #[cfg(target_os = "windows")]
        {
            let _ = std::process::Command::new("cmd")
                .args(["/C", "start", "", &path.to_string_lossy()])
                .spawn();
        }
        
        #[cfg(target_os = "macos")]
        {
            let _ = std::process::Command::new("open")
                .arg(path)
                .spawn();
        }
        
        #[cfg(target_os = "linux")]
        {
            let _ = std::process::Command::new("xdg-open")
                .arg(path)
                .spawn();
        }
    }
    
    fn show_in_explorer(path: &Path) {
        #[cfg(target_os = "windows")]
        {
            let _ = std::process::Command::new("explorer")
                .args(["/select,", &path.to_string_lossy()])
                .spawn();
        }
        
        #[cfg(target_os = "macos")]
        {
            let _ = std::process::Command::new("open")
                .args(["-R", &path.to_string_lossy()])
                .spawn();
        }
        
        #[cfg(target_os = "linux")]
        {
            if let Some(parent) = path.parent() {
                let _ = std::process::Command::new("xdg-open")
                    .arg(parent)
                    .spawn();
            }
        }
    }
}
