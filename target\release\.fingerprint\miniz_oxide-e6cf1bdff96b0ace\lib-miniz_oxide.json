{"rustc": 16591470773350601817, "features": "[\"default\", \"simd\", \"simd-adler32\", \"with-alloc\"]", "declared_features": "[\"alloc\", \"block-boundary\", \"compiler_builtins\", \"core\", \"default\", \"rustc-dep-of-std\", \"serde\", \"simd\", \"simd-adler32\", \"std\", \"with-alloc\"]", "target": 8661567070972402511, "profile": 11301701693415961412, "path": 12710860292882388079, "deps": [[4018467389006652250, "simd_adler32", false, 11657224524450689336], [15407850927583745935, "adler2", false, 9645523776720437747]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\miniz_oxide-e6cf1bdff96b0ace\\dep-lib-miniz_oxide", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}