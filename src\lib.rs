pub mod core;
pub mod gui;
pub mod utils;

#[cfg(test)]
mod tests {
    use super::*;
    use std::path::PathBuf;

    #[test]
    fn test_file_filter_default() {
        let filter = core::FileFilter::default();
        assert!(filter.extensions.is_none());
        assert!(filter.mime_types.is_none());
        assert!(filter.min_size.is_none());
        assert!(filter.max_size.is_none());
    }

    #[test]
    fn test_duplicate_finder_creation() {
        let finder = core::DuplicateFinder::new();
        // Just test that we can create it
        assert_eq!(std::mem::size_of_val(&finder), std::mem::size_of::<usize>());
    }

    #[test]
    fn test_file_info_creation() {
        let file_info = core::FileInfo {
            path: PathBuf::from("test.txt"),
            size: 1024,
            modified: std::time::SystemTime::now(),
            hash: None,
        };
        assert_eq!(file_info.size, 1024);
        assert_eq!(file_info.path, PathBuf::from("test.txt"));
    }

    #[test]
    fn test_utils_format_file_size() {
        assert_eq!(utils::format_file_size(1024), "1.0 KB");
        assert_eq!(utils::format_file_size(1048576), "1.0 MB");
        assert_eq!(utils::format_file_size(500), "500 B");
    }
}
