use eframe::egui;
use std::path::PathBuf;
use std::sync::{Arc, Mutex};
use std::thread;
use rfd::FileDialog;

use crate::core::{File<PERSON>canner, DuplicateFinder, FileFilter, DuplicateSet, FileInfo, ScanPhase};
use super::duplicate_view::DuplicateView;
use super::file_preview::FilePreview;

#[derive(Default)]
struct ScanState {
    duplicate_sets: Vec<DuplicateSet>,
    progress: f32,
    completed: bool,
    files_loaded: usize,
    total_files_to_load: usize,
    comparisons_made: usize,
    total_comparisons: usize,
    current_phase: ScanPhase,
    current_folder: String,
    folders_completed: usize,
    total_folders: usize,
}

pub struct DuplicateFinderApp {
    selected_folders: Vec<PathBuf>,
    filters: FileFilter,
    duplicate_sets: Vec<DuplicateSet>,
    selected_file: Option<FileInfo>,
    scanning: bool,
    scan_progress: f32,
    show_preview: bool,
    scan_state: Arc<Mutex<ScanState>>,
    // UI state for progress display
    files_loaded: usize,
    total_files_to_load: usize,
    comparisons_made: usize,
    total_comparisons: usize,
    current_phase: ScanPhase,
    current_folder: String,
    folders_completed: usize,
    total_folders: usize,
}

impl Default for DuplicateFinderApp {
    fn default() -> Self {
        Self {
            selected_folders: Vec::new(),
            filters: FileFilter::default(),
            duplicate_sets: Vec::new(),
            selected_file: None,
            scanning: false,
            scan_progress: 0.0,
            show_preview: true,
            scan_state: Arc::new(Mutex::new(ScanState::default())),
            files_loaded: 0,
            total_files_to_load: 0,
            comparisons_made: 0,
            total_comparisons: 0,
            current_phase: ScanPhase::Idle,
            current_folder: String::new(),
            folders_completed: 0,
            total_folders: 0,
        }
    }
}

impl eframe::App for DuplicateFinderApp {
    fn update(&mut self, ctx: &egui::Context, _frame: &mut eframe::Frame) {
        // Check for scan completion and update progress
        if self.scanning {
            if let Ok(state) = self.scan_state.try_lock() {
                self.scan_progress = state.progress;
                self.files_loaded = state.files_loaded;
                self.total_files_to_load = state.total_files_to_load;
                self.comparisons_made = state.comparisons_made;
                self.total_comparisons = state.total_comparisons;
                self.current_phase = state.current_phase.clone();
                self.current_folder = state.current_folder.clone();
                self.folders_completed = state.folders_completed;
                self.total_folders = state.total_folders;

                if state.completed {
                    self.duplicate_sets = state.duplicate_sets.clone();
                    self.scanning = false;
                }
            }
        }
        egui::TopBottomPanel::top("top_panel").show(ctx, |ui| {
            ui.horizontal(|ui| {
                if ui.button("Add Folder").clicked() {
                    if let Some(folder) = FileDialog::new().pick_folder() {
                        self.selected_folders.push(folder);
                    }
                }
                
                if ui.button("Clear Folders").clicked() {
                    self.selected_folders.clear();
                }
                
                if ui.button("Scan").clicked() && !self.scanning && !self.selected_folders.is_empty() {
                    self.start_scan(ctx);
                }
                
                if self.scanning {
                    ui.vertical(|ui| {
                        // Overall progress bar
                        ui.add(egui::ProgressBar::new(self.scan_progress).show_percentage());

                        // Detailed progress information
                        match self.current_phase {
                            ScanPhase::LoadingFiles => {
                                if !self.current_folder.is_empty() {
                                    ui.label(format!("Scanning folder {} of {}: {}",
                                                   self.folders_completed + 1,
                                                   self.total_folders,
                                                   self.current_folder));
                                }
                                if self.files_loaded > 0 {
                                    ui.label(format!("Files found so far: {}", self.files_loaded));
                                } else {
                                    ui.label("Starting scan...");
                                }
                            }
                            ScanPhase::GroupingBySize => {
                                ui.label(format!("Grouping {} files by size...", self.files_loaded));
                            }
                            ScanPhase::ComputingPartialHashes => {
                                if self.total_comparisons > 0 {
                                    ui.label(format!("Computing partial hashes: {} / {} comparisons",
                                                   self.comparisons_made, self.total_comparisons));
                                } else {
                                    ui.label("Computing partial hashes...");
                                }
                            }
                            ScanPhase::ComputingFullHashes => {
                                if self.total_comparisons > 0 {
                                    ui.label(format!("Computing full hashes: {} / {} comparisons",
                                                   self.comparisons_made, self.total_comparisons));
                                } else {
                                    ui.label("Computing full hashes...");
                                }
                            }
                            _ => {
                                ui.label("Scanning...");
                            }
                        }
                    });
                }
            });
            
            ui.collapsing("Filters", |ui| {
                ui.horizontal(|ui| {
                    let has_filters = self.filters.extensions.is_some()
                        || self.filters.mime_types.is_some()
                        || self.filters.min_size.is_some()
                        || self.filters.max_size.is_some()
                        || self.filters.min_age.is_some()
                        || self.filters.max_age.is_some();

                    if has_filters {
                        ui.label("🔍 Active filters:");
                        if ui.small_button("Reset All").clicked() {
                            self.filters = FileFilter::default();
                        }
                    } else {
                        ui.label("No filters active");
                    }
                });
                ui.separator();
                ui.horizontal(|ui| {
                    ui.label("Extensions:");
                    if ui.button("Edit").clicked() {
                        // Show extension filter dialog
                    }
                });

                ui.horizontal(|ui| {
                    ui.label("MIME Types:");
                    if ui.button("Edit").clicked() {
                        // Show MIME type filter dialog
                    }
                });

                ui.horizontal(|ui| {
                    ui.label("Size Range:");
                    ui.label("Min:");
                    let mut min_size_mb = self.filters.min_size.unwrap_or(0) as f64 / 1_048_576.0;
                    if ui.add(egui::DragValue::new(&mut min_size_mb).suffix(" MB").speed(0.1)).changed() {
                        self.filters.min_size = if min_size_mb > 0.0 {
                            Some((min_size_mb * 1_048_576.0) as u64)
                        } else {
                            None
                        };
                    }

                    ui.label("Max:");
                    let mut max_size_mb = self.filters.max_size.map(|s| s as f64 / 1_048_576.0).unwrap_or(1000.0);
                    if ui.add(egui::DragValue::new(&mut max_size_mb).suffix(" MB").speed(0.1)).changed() {
                        self.filters.max_size = if max_size_mb > 0.0 {
                            Some((max_size_mb * 1_048_576.0) as u64)
                        } else {
                            None
                        };
                    }
                });

                ui.horizontal(|ui| {
                    ui.label("Age Range:");
                    ui.label("Min days old:");
                    let mut min_age_days = self.filters.min_age
                        .and_then(|t| std::time::SystemTime::now().duration_since(t).ok())
                        .map(|d| (d.as_secs() / 86400) as f64)
                        .unwrap_or(0.0);
                    if ui.add(egui::DragValue::new(&mut min_age_days).suffix(" days").speed(1.0)).changed() {
                        self.filters.min_age = if min_age_days > 0.0 {
                            std::time::SystemTime::now()
                                .checked_sub(std::time::Duration::from_secs((min_age_days * 86400.0) as u64))
                        } else {
                            None
                        };
                    }

                    ui.label("Max days old:");
                    let mut max_age_days = self.filters.max_age
                        .and_then(|t| std::time::SystemTime::now().duration_since(t).ok())
                        .map(|d| (d.as_secs() / 86400) as f64)
                        .unwrap_or(365.0);
                    if ui.add(egui::DragValue::new(&mut max_age_days).suffix(" days").speed(1.0)).changed() {
                        self.filters.max_age = if max_age_days > 0.0 {
                            std::time::SystemTime::now()
                                .checked_sub(std::time::Duration::from_secs((max_age_days * 86400.0) as u64))
                        } else {
                            None
                        };
                    }
                });
            });
            
            ui.separator();
            
            ui.horizontal(|ui| {
                ui.label("Selected Folders:");
            });
            
            for folder in &self.selected_folders {
                ui.label(folder.to_string_lossy().to_string());
            }
        });
        
        egui::CentralPanel::default().show(ctx, |ui| {
            ui.heading("Duplicate Files");

            // Show scan summary if we have results
            if !self.duplicate_sets.is_empty() {
                let total_duplicates: usize = self.duplicate_sets.iter().map(|set| set.files.len()).sum();
                let total_sets = self.duplicate_sets.len();
                let wasted_space: u64 = self.duplicate_sets.iter()
                    .map(|set| (set.files.len() - 1) as u64 * set.files[0].size)
                    .sum();

                ui.horizontal(|ui| {
                    ui.label(format!("📊 Found {} duplicate sets containing {} files", total_sets, total_duplicates));
                    ui.separator();
                    ui.label(format!("💾 Wasted space: {}", crate::utils::format_file_size(wasted_space)));
                });
                ui.separator();
            }

            if self.duplicate_sets.is_empty() && !self.scanning {
                ui.centered_and_justified(|ui| {
                    ui.label("No duplicates found. Add folders and click Scan to begin.");
                });
            } else {
                DuplicateView::show_duplicate_sets(ui, &self.duplicate_sets, &mut self.selected_file);
            }
        });
        
        if self.show_preview && self.selected_file.is_some() {
            egui::SidePanel::right("preview_panel").show(ctx, |ui| {
                ui.heading("File Preview");
                if let Some(file) = &self.selected_file {
                    FilePreview::show(ui, file);
                }
            });
        }
    }
}

impl DuplicateFinderApp {
    fn start_scan(&mut self, ctx: &egui::Context) {
        self.scanning = true;
        self.scan_progress = 0.0;
        self.duplicate_sets.clear();

        // Reset UI progress counters
        self.files_loaded = 0;
        self.total_files_to_load = 0;
        self.comparisons_made = 0;
        self.total_comparisons = 0;
        self.current_phase = ScanPhase::LoadingFiles;
        self.current_folder = String::new();
        self.folders_completed = 0;
        self.total_folders = self.selected_folders.len();

        // Reset scan state
        if let Ok(mut state) = self.scan_state.lock() {
            *state = ScanState::default();
            state.current_phase = ScanPhase::LoadingFiles;
        }

        let folders = self.selected_folders.clone();
        let filters = self.filters.clone();
        let scan_state = Arc::clone(&self.scan_state);
        let ctx_clone = ctx.clone();

        thread::spawn(move || {
            let scanner = FileScanner::new(filters);
            let finder = DuplicateFinder::new();

            let mut all_files = Vec::new();
            let folder_count = folders.len();

            // Phase 1: Scan directories and load files
            if let Ok(mut state) = scan_state.lock() {
                state.current_phase = ScanPhase::LoadingFiles;
                state.total_folders = folder_count;
                state.folders_completed = 0;
            }
            ctx_clone.request_repaint();

            for (i, folder) in folders.iter().enumerate() {
                // Update current folder being scanned
                let folder_name = folder.file_name()
                    .unwrap_or_default()
                    .to_string_lossy()
                    .to_string();

                if let Ok(mut state) = scan_state.lock() {
                    state.current_folder = folder_name;
                    state.folders_completed = i;
                }
                ctx_clone.request_repaint();

                // Scan the directory with progress updates
                let files = scanner.scan_directory_with_progress(folder, |file_count| {
                    if let Ok(mut state) = scan_state.lock() {
                        state.files_loaded = all_files.len() + file_count;
                    }
                    ctx_clone.request_repaint();
                });

                all_files.extend(files);

                // Update completion progress
                if let Ok(mut state) = scan_state.lock() {
                    state.files_loaded = all_files.len();
                    state.folders_completed = i + 1;
                    state.progress = (i + 1) as f32 / folder_count as f32 * 0.3; // 30% for loading
                }
                ctx_clone.request_repaint();
            }

            // Phase 2: Group by size
            if let Ok(mut state) = scan_state.lock() {
                state.current_phase = ScanPhase::GroupingBySize;
                state.progress = 0.3;
            }
            ctx_clone.request_repaint();

            // Phase 3: Find duplicates with detailed progress
            if let Ok(mut state) = scan_state.lock() {
                state.current_phase = ScanPhase::ComputingPartialHashes;
                state.progress = 0.4;
            }
            ctx_clone.request_repaint();

            let duplicate_sets = finder.find_duplicates_with_progress(all_files, |phase, made, total| {
                if let Ok(mut state) = scan_state.lock() {
                    state.current_phase = phase;
                    state.comparisons_made = made;
                    state.total_comparisons = total;

                    // Progress: 30% loading, 10% grouping, 60% for duplicate finding
                    let phase_progress = if total > 0 { made as f32 / total as f32 } else { 0.0 };
                    state.progress = 0.4 + phase_progress * 0.6;
                }
                ctx_clone.request_repaint();
            });

            // Update final state
            if let Ok(mut state) = scan_state.lock() {
                state.duplicate_sets = duplicate_sets;
                state.progress = 1.0;
                state.completed = true;
                state.current_phase = ScanPhase::Completed;
            }
            ctx_clone.request_repaint();
        });
    }

    fn show_in_explorer(path: &std::path::Path) {
        #[cfg(target_os = "windows")]
        {
            let _ = std::process::Command::new("explorer")
                .args(["/select,", &path.to_string_lossy()])
                .spawn();
        }

        #[cfg(target_os = "macos")]
        {
            let _ = std::process::Command::new("open")
                .args(["-R", &path.to_string_lossy()])
                .spawn();
        }

        #[cfg(target_os = "linux")]
        {
            if let Some(parent) = path.parent() {
                let _ = std::process::Command::new("xdg-open")
                    .arg(parent)
                    .spawn();
            }
        }
    }
}