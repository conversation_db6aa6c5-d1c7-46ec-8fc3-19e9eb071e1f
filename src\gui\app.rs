use eframe::{egui, epi};
use std::path::PathBuf;
use std::sync::{Arc, Mutex};
use std::thread;
use rfd::FileDialog;

use crate::core::{<PERSON><PERSON>canner, DuplicateFinder, FileFilter, DuplicateSet, FileInfo, FileOperations};
use super::duplicate_view::DuplicateView;
use super::file_preview::FilePreview;

#[derive(Default)]
struct ScanState {
    duplicate_sets: Vec<DuplicateSet>,
    progress: f32,
    completed: bool,
}

pub struct DuplicateFinderApp {
    selected_folders: Vec<PathBuf>,
    filters: FileFilter,
    duplicate_sets: Vec<DuplicateSet>,
    selected_file: Option<FileInfo>,
    scanning: bool,
    scan_progress: f32,
    show_preview: bool,
    scan_state: Arc<Mutex<ScanState>>,
}

impl Default for DuplicateFinderApp {
    fn default() -> Self {
        Self {
            selected_folders: Vec::new(),
            filters: FileFilter::default(),
            duplicate_sets: Vec::new(),
            selected_file: None,
            scanning: false,
            scan_progress: 0.0,
            show_preview: true,
            scan_state: Arc::new(Mutex::new(ScanState::default())),
        }
    }
}

impl epi::App for DuplicateFinderApp {
    fn name(&self) -> &str {
        "Duplicate File Finder"
    }

    fn update(&mut self, ctx: &egui::Context, _frame: &epi::Frame) {
        // Check for scan completion
        if self.scanning {
            if let Ok(state) = self.scan_state.try_lock() {
                self.scan_progress = state.progress;
                if state.completed {
                    self.duplicate_sets = state.duplicate_sets.clone();
                    self.scanning = false;
                }
            }
        }
        egui::TopBottomPanel::top("top_panel").show(ctx, |ui| {
            ui.horizontal(|ui| {
                if ui.button("Add Folder").clicked() {
                    if let Some(folder) = FileDialog::new().pick_folder() {
                        self.selected_folders.push(folder);
                    }
                }
                
                if ui.button("Clear Folders").clicked() {
                    self.selected_folders.clear();
                }
                
                if ui.button("Scan").clicked() && !self.scanning && !self.selected_folders.is_empty() {
                    self.start_scan(ctx);
                }
                
                if self.scanning {
                    ui.add(egui::ProgressBar::new(self.scan_progress).show_percentage());
                }
            });
            
            ui.collapsing("Filters", |ui| {
                ui.horizontal(|ui| {
                    ui.label("Extensions:");
                    if ui.button("Edit").clicked() {
                        // Show extension filter dialog
                    }
                });
                
                ui.horizontal(|ui| {
                    ui.label("MIME Types:");
                    if ui.button("Edit").clicked() {
                        // Show MIME type filter dialog
                    }
                });
                
                ui.horizontal(|ui| {
                    ui.label("Size Range:");
                    // Size filter controls
                });
                
                ui.horizontal(|ui| {
                    ui.label("Age Range:");
                    // Age filter controls
                });
            });
            
            ui.separator();
            
            ui.horizontal(|ui| {
                ui.label("Selected Folders:");
            });
            
            for folder in &self.selected_folders {
                ui.label(folder.to_string_lossy().to_string());
            }
        });
        
        egui::CentralPanel::default().show(ctx, |ui| {
            ui.heading("Duplicate Files");
            
            if self.duplicate_sets.is_empty() && !self.scanning {
                ui.centered_and_justified(|ui| {
                    ui.label("No duplicates found. Add folders and click Scan to begin.");
                });
            } else {
                DuplicateView::show_duplicate_sets(ui, &self.duplicate_sets, &mut self.selected_file);
            }
        });
        
        if self.show_preview && self.selected_file.is_some() {
            egui::SidePanel::right("preview_panel").show(ctx, |ui| {
                ui.heading("File Preview");
                if let Some(file) = &self.selected_file {
                    FilePreview::show(ui, file);
                }
            });
        }
    }
}

impl DuplicateFinderApp {
    fn start_scan(&mut self, ctx: &egui::Context) {
        self.scanning = true;
        self.scan_progress = 0.0;
        self.duplicate_sets.clear();

        // Reset scan state
        if let Ok(mut state) = self.scan_state.lock() {
            *state = ScanState::default();
        }

        let folders = self.selected_folders.clone();
        let filters = self.filters.clone();
        let scan_state = Arc::clone(&self.scan_state);
        let ctx_clone = ctx.clone();

        thread::spawn(move || {
            let scanner = FileScanner::new(filters);
            let finder = DuplicateFinder::new();

            let mut all_files = Vec::new();
            let folder_count = folders.len();

            // Scan directories
            for (i, folder) in folders.iter().enumerate() {
                let files = scanner.scan_directory(folder);
                all_files.extend(files);

                let progress = (i + 1) as f32 / folder_count as f32 * 0.5;
                if let Ok(mut state) = scan_state.lock() {
                    state.progress = progress;
                }
                ctx_clone.request_repaint();
            }

            // Find duplicates
            let duplicate_sets = finder.find_duplicates(all_files);

            // Update final state
            if let Ok(mut state) = scan_state.lock() {
                state.duplicate_sets = duplicate_sets;
                state.progress = 1.0;
                state.completed = true;
            }
            ctx_clone.request_repaint();
        });
    }

    fn show_in_explorer(path: &std::path::Path) {
        #[cfg(target_os = "windows")]
        {
            let _ = std::process::Command::new("explorer")
                .args(["/select,", &path.to_string_lossy()])
                .spawn();
        }

        #[cfg(target_os = "macos")]
        {
            let _ = std::process::Command::new("open")
                .args(["-R", &path.to_string_lossy()])
                .spawn();
        }

        #[cfg(target_os = "linux")]
        {
            if let Some(parent) = path.parent() {
                let _ = std::process::Command::new("xdg-open")
                    .arg(parent)
                    .spawn();
            }
        }
    }
}