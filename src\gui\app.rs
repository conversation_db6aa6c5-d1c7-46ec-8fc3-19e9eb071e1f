use eframe::{egui, epi};
use std::path::PathBuf;
use std::sync::{Arc, Mutex};
use std::thread;
use rfd::FileDialog;

use crate::core::{<PERSON>Scanner, DuplicateFinder, FileFilter, DuplicateSet, FileInfo, FileOperations};
use super::duplicate_view::DuplicateView;
use super::file_preview::FilePreview;

pub struct DuplicateFinderApp {
    selected_folders: Vec<PathBuf>,
    filters: FileFilter,
    duplicate_sets: Vec<DuplicateSet>,
    selected_file: Option<FileInfo>,
    scanning: bool,
    scan_progress: f32,
    show_preview: bool,
}

impl Default for DuplicateFinderApp {
    fn default() -> Self {
        Self {
            selected_folders: Vec::new(),
            filters: FileFilter::default(),
            duplicate_sets: Vec::new(),
            selected_file: None,
            scanning: false,
            scan_progress: 0.0,
            show_preview: true,
        }
    }
}

impl epi::App for DuplicateFinderApp {
    fn name(&self) -> &str {
        "Duplicate File Finder"
    }

    fn update(&mut self, ctx: &egui::Context, _frame: &epi::Frame) {
        egui::TopBottomPanel::top("top_panel").show(ctx, |ui| {
            ui.horizontal(|ui| {
                if ui.button("Add Folder").clicked() {
                    if let Some(folder) = FileDialog::new().pick_folder() {
                        self.selected_folders.push(folder);
                    }
                }
                
                if ui.button("Clear Folders").clicked() {
                    self.selected_folders.clear();
                }
                
                if ui.button("Scan").clicked() && !self.scanning && !self.selected_folders.is_empty() {
                    self.start_scan();
                }
                
                if self.scanning {
                    ui.add(egui::ProgressBar::new(self.scan_progress).show_percentage());
                }
            });
            
            ui.collapsing("Filters", |ui| {
                ui.horizontal(|ui| {
                    ui.label("Extensions:");
                    if ui.button("Edit").clicked() {
                        // Show extension filter dialog
                    }
                });
                
                ui.horizontal(|ui| {
                    ui.label("MIME Types:");
                    if ui.button("Edit").clicked() {
                        // Show MIME type filter dialog
                    }
                });
                
                ui.horizontal(|ui| {
                    ui.label("Size Range:");
                    // Size filter controls
                });
                
                ui.horizontal(|ui| {
                    ui.label("Age Range:");
                    // Age filter controls
                });
            });
            
            ui.separator();
            
            ui.horizontal(|ui| {
                ui.label("Selected Folders:");
            });
            
            for folder in &self.selected_folders {
                ui.label(folder.to_string_lossy().to_string());
            }
        });
        
        egui::CentralPanel::default().show(ctx, |ui| {
            ui.heading("Duplicate Files");
            
            if self.duplicate_sets.is_empty() && !self.scanning {
                ui.centered_and_justified(|ui| {
                    ui.label("No duplicates found. Add folders and click Scan to begin.");
                });
            } else {
                self.show_duplicate_sets(ui);
            }
        });
        
        if self.show_preview && self.selected_file.is_some() {
            egui::SidePanel::right("preview_panel").show(ctx, |ui| {
                ui.heading("File Preview");
                if let Some(file) = &self.selected_file {
                    FilePreview::show(ui, file);
                }
            });
        }
    }
}

impl DuplicateFinderApp {
    fn start_scan(&mut self) {
        self.scanning = true;
        self.scan_progress = 0.0;
        self.duplicate_sets.clear();
        
        let folders = self.selected_folders.clone();
        let filters = self.filters.clone();
        let app_data = Arc::new(Mutex::new((Vec::new(), 0.0)));
        let app_data_clone = app_data.clone();
        
        thread::spawn(move || {
            let scanner = FileScanner::new(filters);
            let finder = DuplicateFinder::new();
            
            let mut all_files = Vec::new();
            let folder_count = folders.len();
            
            for (i, folder) in folders.iter().enumerate() {
                let files = scanner.scan_directory(folder);
                all_files.extend(files);
                
                let progress = (i + 1) as f32 / folder_count as f32 * 0.5;
                app_data.lock().unwrap().1 = progress;
            }
            
            let duplicate_sets = finder.find_duplicates(all_files);
            
            let mut data = app_data.lock().unwrap();
            data.0 = duplicate_sets;
            data.1 = 1.0;
        });
        
        // Poll for results
        let ctx_clone = egui::Context::clone(ctx);
        thread::spawn(move || {
            while app_data_clone.lock().unwrap().1 < 1.0 {
                thread::sleep(std::time::Duration::from_millis(100));
                ctx_clone.request_repaint();
            }
        });
    }
    
    fn show_duplicate_sets(&mut self, ui: &mut egui::Ui) {
        egui::ScrollArea::vertical().show(ui, |ui| {
            for (i, set) in self.duplicate_sets.iter().enumerate() {
                ui.collapsing(format!("Duplicate Set {} - {} files - {} bytes", i + 1, set.files.len(), set.files[0].size), |ui| {
                    ui.horizontal(|ui| {
                        if ui.button("Delete All But Newest").clicked() {
                            let paths: Vec<PathBuf> = set.files.iter().map(|f| f.path.clone()).collect();
                            let _ = FileOperations::preserve_newest(&paths);
                        }
                        
                        if ui.button("Delete All But Oldest").clicked() {
                            let paths: Vec<PathBuf> = set.files.iter().map(|f| f.path.clone()).collect();
                            let _ = FileOperations::preserve_oldest(&paths);
                        }
                    });
                    
                    for file in &set.files {
                        ui.horizontal(|ui| {
                            if ui.selectable_label(
                                self.selected_file.as_ref().map_or(false, |f| f.path == file.path),
                                file.path.to_string_lossy().to_string()
                            ).clicked() {
                                self.selected_file = Some(file.clone());
                            }
                            
                            if ui.button("Delete").clicked() {
                                let _ = FileOperations::delete_file(&file.path);
                            }
                            
                            if