use std::collections::HashMap;
use std::fs::File;
use std::io::{self, Read};
use sha2::{Sha256, Digest};
use rayon::prelude::*;
use super::file_scanner::FileInfo;

#[derive(Clone)]
pub struct DuplicateSet {
    pub files: Vec<FileInfo>,
}

pub struct DuplicateFinder {
    chunk_size: usize,
}

impl DuplicateFinder {
    pub fn new() -> Self {
        Self { chunk_size: 4096 }
    }

    pub fn find_duplicates(&self, files: Vec<FileInfo>) -> Vec<DuplicateSet> {
        // Step 1: Group by size
        let size_groups = self.group_by_size(files);
        
        // Step 2: Check partial hashes
        let partial_hash_groups = self.check_partial_hashes(size_groups);
        
        // Step 3: Compute full hashes
        self.check_full_hashes(partial_hash_groups)
    }

    fn group_by_size(&self, files: Vec<FileInfo>) -> Vec<Vec<FileInfo>> {
        let mut size_map: HashMap<u64, Vec<FileInfo>> = HashMap::new();
        
        for file in files {
            size_map.entry(file.size).or_default().push(file);
        }
        
        // Only keep groups with at least 2 files (potential duplicates)
        size_map.into_iter()
            .filter(|(_, group)| group.len() > 1)
            .map(|(_, group)| group)
            .collect()
    }

    fn check_partial_hashes(&self, size_groups: Vec<Vec<FileInfo>>) -> Vec<Vec<FileInfo>> {
        size_groups.into_par_iter()
            .filter_map(|group| {
                let mut partial_hash_map: HashMap<String, Vec<FileInfo>> = HashMap::new();
                
                for file in group {
                    match self.compute_partial_hash(&file.path) {
                        Ok(hash) => {
                            partial_hash_map.entry(hash).or_default().push(file);
                        }
                        Err(_) => continue,
                    }
                }
                
                // Only keep groups with at least 2 files
                let potential_duplicates: Vec<Vec<FileInfo>> = partial_hash_map.into_iter()
                    .filter(|(_, group)| group.len() > 1)
                    .map(|(_, group)| group)
                    .collect();
                
                if potential_duplicates.is_empty() {
                    None
                } else {
                    Some(potential_duplicates)
                }
            })
            .flatten()
            .collect()
    }

    fn check_full_hashes(&self, groups: Vec<Vec<FileInfo>>) -> Vec<DuplicateSet> {
        groups.into_par_iter()
            .filter_map(|group| {
                let mut full_hash_map: HashMap<String, Vec<FileInfo>> = HashMap::new();
                
                for mut file in group {
                    match self.compute_full_hash(&file.path) {
                        Ok(hash) => {
                            file.hash = Some(hash.clone());
                            full_hash_map.entry(hash).or_default().push(file);
                        }
                        Err(_) => continue,
                    }
                }
                
                // Convert to duplicate sets
                let duplicate_sets: Vec<DuplicateSet> = full_hash_map.into_iter()
                    .filter(|(_, files)| files.len() > 1)
                    .map(|(_, files)| DuplicateSet { files })
                    .collect();
                
                if duplicate_sets.is_empty() {
                    None
                } else {
                    Some(duplicate_sets)
                }
            })
            .flatten()
            .collect()
    }

    fn compute_partial_hash(&self, path: &std::path::Path) -> io::Result<String> {
        let mut file = File::open(path)?;
        let mut buffer = vec![0; self.chunk_size];
        let bytes_read = file.read(&mut buffer)?;
        buffer.truncate(bytes_read);
        
        let mut hasher = Sha256::new();
        hasher.update(&buffer);
        let result = hasher.finalize();
        
        Ok(format!("{:x}", result))
    }

    fn compute_full_hash(&self, path: &std::path::Path) -> io::Result<String> {
        let mut file = File::open(path)?;
        let mut hasher = Sha256::new();
        let mut buffer = vec![0; self.chunk_size];
        
        loop {
            let bytes_read = file.read(&mut buffer)?;
            if bytes_read == 0 {
                break;
            }
            hasher.update(&buffer[..bytes_read]);
        }
        
        let result = hasher.finalize();
        Ok(format!("{:x}", result))
    }
}