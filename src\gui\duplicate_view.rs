use eframe::egui;
use std::path::PathBuf;
use crate::core::{DuplicateSet, FileInfo, FileOperations};
use crate::utils::{format_file_size, get_file_icon, truncate_path};
use chrono::{DateTime, Utc};

pub struct DuplicateView;

impl DuplicateView {
    pub fn show_duplicate_sets(
        ui: &mut egui::Ui, 
        duplicate_sets: &[DuplicateSet],
        selected_file: &mut Option<FileInfo>
    ) {
        egui::ScrollArea::vertical().show(ui, |ui| {
            for (i, set) in duplicate_sets.iter().enumerate() {
                if set.files.is_empty() {
                    continue;
                }
                
                let total_size = set.files.len() as u64 * set.files[0].size;
                let wasted_size = (set.files.len() - 1) as u64 * set.files[0].size;
                
                ui.collapsing(
                    format!("Duplicate Set {} - {} files - {} total ({} wasted)", 
                           i + 1, 
                           set.files.len(), 
                           format_file_size(total_size),
                           format_file_size(wasted_size)
                    ), 
                    |ui| {
                        Self::show_duplicate_set_actions(ui, set);
                        ui.separator();
                        Self::show_duplicate_files(ui, set, selected_file);
                    }
                );
                
                ui.add_space(5.0);
            }
        });
    }
    
    fn show_duplicate_set_actions(ui: &mut egui::Ui, set: &DuplicateSet) {
        ui.horizontal(|ui| {
            if ui.button("🗑️ Delete All But Newest").clicked() {
                let paths: Vec<PathBuf> = set.files.iter().map(|f| f.path.clone()).collect();
                if let Err(e) = FileOperations::preserve_newest(&paths) {
                    eprintln!("Error preserving newest: {}", e);
                }
            }
            
            if ui.button("🗑️ Delete All But Oldest").clicked() {
                let paths: Vec<PathBuf> = set.files.iter().map(|f| f.path.clone()).collect();
                if let Err(e) = FileOperations::preserve_oldest(&paths) {
                    eprintln!("Error preserving oldest: {}", e);
                }
            }
            
            if ui.button("📊 Compare Files").clicked() {
                // TODO: Implement file comparison view
            }
        });
    }
    
    fn show_duplicate_files(
        ui: &mut egui::Ui, 
        set: &DuplicateSet, 
        selected_file: &mut Option<FileInfo>
    ) {
        for file in &set.files {
            ui.horizontal(|ui| {
                // File icon
                ui.label(get_file_icon(&file.path));
                
                // File selection
                let is_selected = selected_file.as_ref()
                    .map_or(false, |f| f.path == file.path);
                
                if ui.selectable_label(
                    is_selected,
                    truncate_path(&file.path, 60)
                ).clicked() {
                    *selected_file = Some(file.clone());
                }
                
                ui.with_layout(egui::Layout::right_to_left(egui::Align::Center), |ui| {
                    // Individual file actions
                    if ui.small_button("🗑️").on_hover_text("Delete this file").clicked() {
                        if let Err(e) = FileOperations::delete_file(&file.path) {
                            eprintln!("Error deleting file: {}", e);
                        }
                    }
                    
                    if ui.small_button("♻️").on_hover_text("Move to recycle bin").clicked() {
                        if let Err(e) = FileOperations::recycle_file(&file.path) {
                            eprintln!("Error recycling file: {}", e);
                        }
                    }
                    
                    if ui.small_button("📂").on_hover_text("Show in explorer").clicked() {
                        Self::show_in_explorer(&file.path);
                    }
                    
                    if ui.small_button("📋").on_hover_text("Copy path").clicked() {
                        ui.output_mut(|o| o.copied_text = file.path.to_string_lossy().to_string());
                    }
                    
                    // File size and date
                    ui.label(format_file_size(file.size));
                    
                    if let Ok(modified) = file.modified.duration_since(std::time::UNIX_EPOCH) {
                        let datetime = chrono::DateTime::from_timestamp(modified.as_secs() as i64, 0)
                            .unwrap_or_default();
                        ui.label(datetime.format("%Y-%m-%d").to_string());
                    }
                });
            });
        }
    }
    
    fn show_in_explorer(path: &std::path::Path) {
        #[cfg(target_os = "windows")]
        {
            let _ = std::process::Command::new("explorer")
                .args(["/select,", &path.to_string_lossy()])
                .spawn();
        }
        
        #[cfg(target_os = "macos")]
        {
            let _ = std::process::Command::new("open")
                .args(["-R", &path.to_string_lossy()])
                .spawn();
        }
        
        #[cfg(target_os = "linux")]
        {
            if let Some(parent) = path.parent() {
                let _ = std::process::Command::new("xdg-open")
                    .arg(parent)
                    .spawn();
            }
        }
    }
}
