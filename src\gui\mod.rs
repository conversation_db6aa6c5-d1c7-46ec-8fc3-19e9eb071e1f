mod app;
mod file_preview;
mod duplicate_view;

use app::DuplicateFinderApp;

pub fn launch_app() -> Result<(), eframe::Error> {
    let options = eframe::NativeOptions {
        viewport: egui::ViewportBuilder::default().with_inner_size([1200.0, 800.0]),
        ..Default::default()
    };

    eframe::run_native(
        "Duplicate File Finder",
        options,
        Box::new(|_cc| Box::new(DuplicateFinderApp::default())),
    )
}